import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { apiService } from '../services/api';
import { handleAuthError } from '../utils/authErrorHandler';
import { useClearFiltersListener } from '../utils/scrollPreservation';
import { Card, CardHeader, CardBody, Table, TableHead, TableBody, TableRow, TableHeader, TableCell, Badge, Spinner, Alert, Button, ServiceStatusBadge, PriorityBadge, ServiceCallsEmptyState, CustomersEmptyState, RecentActivityEmptyState } from '../components/ui';
import LoadingScreen from '../components/ui/LoadingScreen';
import StatusChart, { StatusDonutChart } from '../components/dashboard/StatusChart';
import ExecutiveWorkload, { ExecutiveSummary } from '../components/dashboard/ExecutiveWorkload';
import AnalyticsDashboard from '../components/dashboard/AnalyticsDashboard';
import {
  groupServiceCallsByCustomer,
  consolidateServiceCallDescriptions,
  getStatusDistribution,
  getPriorityDistribution,
  getExecutiveWorkload,
  consolidateCustomerInfo,
  formatRelativeDate
} from '../utils/dashboardHelpers';

const Dashboard = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview'); // 'overview' or 'analytics'
  const [stats, setStats] = useState({
    totalCustomers: 0,
    activeServices: 0,
    monthlyRevenue: 0,
    openServiceCalls: 0
  });
  const [recentCustomers, setRecentCustomers] = useState([]);
  const [recentServiceCalls, setRecentServiceCalls] = useState([]);
  const [allServiceCalls, setAllServiceCalls] = useState([]); // For calculations (charts, distributions)
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // Removed date filters as per user request

  // Task 5: Unified search functionality
  const [unifiedSearchTerm, setUnifiedSearchTerm] = useState('');
  const [unifiedSearchResults, setUnifiedSearchResults] = useState([]);
  const [unifiedSearchLoading, setUnifiedSearchLoading] = useState(false);

  // Handle clear filters event from EmptyState component
  const clearAllFilters = () => {
    setUnifiedSearchTerm('');
    setUnifiedSearchResults([]);
  };

  // Listen for clear filters events
  useClearFiltersListener(clearAllFilters);

  useEffect(() => {
    fetchDashboardData();
  }, []); // Fetch data once on mount

  // Task 5: Unified search function
  const performUnifiedSearch = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setUnifiedSearchResults([]);
      return;
    }

    // Preserve scroll position during search
    const currentScrollY = window.scrollY;

    setUnifiedSearchLoading(true);
    try {
      // Search customers, service calls, and executives in parallel
      const [customersResponse, serviceCallsResponse, executivesResponse] = await Promise.all([
        apiService.get('/customers', {
          params: {
            search: searchTerm,
            limit: 5,
            fields: 'company_name,display_name,email,phone,customer_code,tally_serial_number'
          }
        }),
        apiService.get('/service-calls', {
          params: {
            search: searchTerm,
            limit: 5,
            fields: 'call_number,customer_name,description,status,priority,assigned_to,created_at'
          }
        }),
        apiService.get('/executives', {
          params: {
            search: searchTerm,
            limit: 5,
            fields: 'first_name,last_name,email,phone,employee_code,designation'
          }
        })
      ]);



      const results = [];

      // Add customer results
      if (customersResponse.data?.success && customersResponse.data?.data?.customers) {
        customersResponse.data.data.customers.forEach(customer => {
          results.push({
            id: customer.id,
            type: 'customer',
            title: customer.company_name || customer.display_name,
            subtitle: customer.email || customer.phone,
            description: `Tally Serial: ${customer.tally_serial_number || customer.customer_code || 'N/A'}`,
            status: customer.is_active ? 'Active' : 'Inactive',
            createdAt: customer.created_at,
            data: customer
          });
        });
      }

      // Add service call results
      if (serviceCallsResponse.data?.success && serviceCallsResponse.data?.data?.serviceCalls) {
        serviceCallsResponse.data.data.serviceCalls.forEach(service => {
          results.push({
            id: service.id,
            type: 'service',
            title: service.call_number || `SRV-${service.id}`,
            subtitle: service.customer?.company_name || service.customer_name || 'Unknown Customer',
            description: service.description || service.subject || 'No description',
            status: service.status?.name || service.status || 'Pending',
            createdAt: service.created_at,
            data: service
          });
        });
      }

      // Add executive results
      if (executivesResponse.data?.success && executivesResponse.data?.data?.executives) {
        executivesResponse.data.data.executives.forEach(executive => {
          const fullName = `${executive.first_name || ''} ${executive.last_name || ''}`.trim() || 'Unknown Executive';
          results.push({
            id: executive.id,
            type: 'executive',
            title: fullName,
            subtitle: executive.designation?.name || executive.employee_code || 'Executive', // Extract name from designation object
            description: executive.email || executive.phone || 'No contact info',
            status: executive.is_active ? 'Active' : 'Inactive',
            createdAt: executive.created_at,
            data: executive
          });
        });
      }

      setUnifiedSearchResults(results);
    } catch (error) {
      console.error('Unified search failed:', error);
      setUnifiedSearchResults([]);
    } finally {
      setUnifiedSearchLoading(false);

      // Restore scroll position after search
      setTimeout(() => {
        window.scrollTo(0, currentScrollY);
      }, 0);
    }
  };

  // Task 5: Debounced unified search handler
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performUnifiedSearch(unifiedSearchTerm);
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [unifiedSearchTerm]);

  // Removed dropdown click outside handler as filters are no longer needed

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching dashboard data');

      // No filters - fetch all data with default parameters
      const dashboardQuery = '';
      // For dashboard calculations, we need all data, not just recent records
      // Only limit the display tables, not the calculation datasets
      const customersQuery = '?limit=1000&sort=created_at&order=desc'; // Increased for accurate calculations
      const serviceCallsQuery = '?limit=1000&sort=created_at&order=desc'; // Increased for accurate calculations

      // Fetch dashboard data from real API endpoints
      const [dashboardRes, customersRes, serviceCallsRes] = await Promise.all([
        apiService.get(`/dashboard/overview${dashboardQuery}`).catch((err) => {
          console.log('Dashboard overview API failed:', err.response?.status, err.response?.data?.message || err.message);
          return { data: { success: false, data: null } };
        }),
        apiService.get(`/customers${customersQuery}`).catch((err) => {
          console.log('Customers API failed:', err.response?.status, err.response?.data?.message || err.message);
          return { data: { success: false, data: { customers: [] } } };
        }),
        apiService.get(`/service-calls${serviceCallsQuery}`).catch((err) => {
          console.log('Service calls API failed:', err.response?.status, err.response?.data?.message || err.message);
          return { data: { success: false, data: { serviceCalls: [] } } };
        })
      ]);

      console.log('📊 API Responses:', {
        dashboard: {
          success: dashboardRes.data?.success,
          hasData: !!dashboardRes.data?.data,
          error: dashboardRes.data?.message
        },
        customers: {
          success: customersRes.data?.success,
          count: customersRes.data?.data?.customers?.length || 0,
          error: customersRes.data?.message
        },
        serviceCalls: {
          success: serviceCallsRes.data?.success,
          count: serviceCallsRes.data?.data?.serviceCalls?.length || 0,
          error: serviceCallsRes.data?.message
        }
      });

      // Debug: Log the actual data structure
      console.log('🔍 Debug - Customers data structure:', customersRes.data?.data);
      console.log('🔍 Debug - Service calls data structure:', serviceCallsRes.data?.data);

      // Extract data from API responses with better error handling
      const customers = Array.isArray(customersRes.data?.data?.customers)
        ? customersRes.data.data.customers
        : [];
      const serviceCalls = Array.isArray(serviceCallsRes.data?.data?.serviceCalls)
        ? serviceCallsRes.data.data.serviceCalls
        : [];

      // Use dashboard overview data if available, otherwise use calculated values
      const dashboardData = dashboardRes.data?.data;
      const hasDashboardData = dashboardRes.data?.success && dashboardData;

      console.log('🔍 Dashboard API Status:', {
        success: dashboardRes.data?.success,
        hasData: !!dashboardData,
        dataKeys: dashboardData ? Object.keys(dashboardData) : []
      });

      if (hasDashboardData) {
        console.log('✅ Using dashboard API data');
        setStats({
          totalCustomers: Number(dashboardData.summary?.totalCustomers) || 0,
          activeServices: Number(dashboardData.summary?.totalServiceCalls) || 0,
          monthlyRevenue: Number(dashboardData.revenue?.salesTotal) || 0,
          openServiceCalls: Number(dashboardData.summary?.openServiceCalls) || 0,
          overdueServiceCalls: Number(dashboardData.summary?.overdueServiceCalls) || 0
        });

        // Set recent data from dashboard
        const recentServiceCallsFromDashboard = dashboardData.recentActivity?.recentServiceCalls;
        if (Array.isArray(recentServiceCallsFromDashboard) && recentServiceCallsFromDashboard.length > 0) {
          setRecentServiceCalls(recentServiceCallsFromDashboard);
        } else {
          setRecentServiceCalls(serviceCalls.slice(0, 5));
        }
      } else {
        console.log('⚠️ Dashboard API failed, using calculated values from individual APIs');
        // Calculate open service calls more accurately
        const openServiceCalls = serviceCalls.filter(sc => {
          const status = sc.status?.name?.toLowerCase() || sc.status?.toLowerCase() || '';
          return status.includes('open') ||
                 status.includes('pending') ||
                 status.includes('new') ||
                 status.includes('assigned') ||
                 sc.status?.category === 'open';
        }).length;

        setStats({
          totalCustomers: customers.length,
          activeServices: serviceCalls.length,
          monthlyRevenue: 0, // Will be calculated from sales data when available
          openServiceCalls,
          overdueServiceCalls: 0 // Will be calculated when dashboard API is available
        });
      }

      // Set recent data with validation
      const validCustomers = customers.filter(customer => customer && (customer.id || customer.company_name));
      const validServiceCalls = serviceCalls.filter(call => call && (call.id || call.call_number));

      // Store all service calls for calculations (charts, distributions, workload)
      setAllServiceCalls(validServiceCalls);

      setRecentCustomers(validCustomers.slice(0, 10));
      if (!hasDashboardData) {
        setRecentServiceCalls(validServiceCalls.slice(0, 10));
      }

      console.log('✅ Dashboard data loaded successfully');
      console.log('📈 Final stats:', {
        totalCustomers: validCustomers.length,
        totalServiceCalls: validServiceCalls.length,
        recentCustomersCount: validCustomers.slice(0, 10).length,
        recentServiceCallsCount: validServiceCalls.slice(0, 10).length,
        dataSource: hasDashboardData ? 'Dashboard API' : 'Individual APIs'
      });

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);

      // Handle authentication errors
      if (handleAuthError(error)) {
        return; // Auth error handled, component will unmount
      }

      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';
      return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Date formatting error:', error);
      return 'N/A';
    }
  };

  // Removed filter helper functions as date filters are no longer needed

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Dashboard..."
        subtitle="Fetching your business insights"
        variant="dashboard"
      />
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <Alert variant="danger" className="mt-6">
          <h4 className="text-lg font-semibold mb-2">Error Loading Dashboard</h4>
          <p className="mb-3">{error}</p>
          <Button variant="outline" onClick={fetchDashboardData}>
            Try Again
          </Button>
        </Alert>
      </div>
    );
  }

  // Calculate percentage changes (mock data for now - in real app, compare with previous period)
  const calculateChange = (current, previous = 0) => {
    if (previous === 0) return '+0%';
    const change = ((current - previous) / previous) * 100;
    return `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`;
  };

  const statsCards = [
    {
      title: 'Total customers',
      value: stats.totalCustomers,
      icon: 'bi-people',
      color: 'theme',
      change: '+4.6%',
      changeType: 'positive',
      subtitle: 'vs last month'
    },
    {
      title: 'Active services',
      value: stats.activeServices,
      icon: 'bi-tools',
      color: 'theme',
      change: '+2.1%',
      changeType: 'positive',
      subtitle: 'vs last month'
    },
    {
      title: 'Monthly revenue',
      value: formatCurrency(stats.monthlyRevenue),
      icon: 'bi-currency-rupee',
      color: 'theme',
      change: '+8.2%',
      changeType: 'positive',
      subtitle: 'vs last month'
    },
    {
      title: 'Overdue services',
      value: stats.overdueServiceCalls,
      icon: 'bi-exclamation-triangle',
      color: stats.overdueServiceCalls > 0 ? 'danger' : 'theme',
      change: stats.overdueServiceCalls > 0 ? 'Needs attention' : 'All good',
      changeType: stats.overdueServiceCalls > 0 ? 'negative' : 'positive',
      subtitle: 'past scheduled'
    }
  ];


  return (
    <>
      <Helmet>
        <title>Dashboard - TallyCRM</title>
      </Helmet>

      <div className="w-full">
        {/* Page Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-1 sm:mb-2">Dashboard</h1>
              <p className="text-gray-600 text-sm sm:text-base">Welcome back! Here's what's happening with your business today.</p>
            </div>
            {/* Removed date filter controls as per user request */}
          </div>
        </div>

        {/* Dashboard Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analytics'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Analytics
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' ? (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-5 xl:gap-6 mb-6 sm:mb-8 laptop:gap-5">
          {statsCards.map((stat, index) => (
            <Card
              key={index}
              className={`dashboard-stat-card border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer group ${
                stat.color === 'danger' ? 'bg-white border-2 border-gray-300' : 'stats-card-primary'
              }`}
              role="button"
              tabIndex={0}
              aria-label={`${stat.title}: ${stat.value}`}
              onClick={() => {
                // Navigate to relevant section based on stat type
                if (stat.title.toLowerCase().includes('customer')) navigate('/customers');
                else if (stat.title.toLowerCase().includes('service')) navigate('/services');
                else if (stat.title.toLowerCase().includes('revenue')) navigate('/sales');
                else if (stat.title.toLowerCase().includes('overdue')) navigate('/services?filter=overdue');
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  if (stat.title.toLowerCase().includes('customer')) navigate('/customers');
                  else if (stat.title.toLowerCase().includes('service')) navigate('/services');
                  else if (stat.title.toLowerCase().includes('revenue')) navigate('/sales');
                  else if (stat.title.toLowerCase().includes('overdue')) navigate('/services?filter=overdue');
                }
              }}
            >
              <CardBody className="p-4 sm:p-6 laptop:p-6">
                <div className="flex items-start justify-between mb-3 sm:mb-4 laptop:mb-4">
                  <div className="flex items-center min-w-0 flex-1">
                    <div className={`rounded-lg p-2 sm:p-3 laptop:p-3.5 mr-2 sm:mr-3 laptop:mr-4 flex-shrink-0 ${
                      stat.color === 'danger'
                        ? 'bg-gray-100'
                        : 'bg-white/20'
                    }`}
                    >
                      <i className={`bi ${stat.icon} text-lg sm:text-2xl laptop:text-2xl`} style={{
                        color: stat.color === 'danger' ? 'var(--primary-color, #2f69b3)' : 'var(--primary-text, #ffffff)'
                      }}
                      >
                      </i>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className={`text-xs sm:text-sm laptop:text-sm font-medium uppercase tracking-wide ${
                        stat.title.toLowerCase().includes('overdue') ? 'no-truncate' : 'truncate'
                      }`} style={{
                        color: stat.color === 'danger' ? '#374151' : 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)'
                      }}
                      title={stat.title} // Add tooltip for accessibility
                      >
                        {stat.title}
                      </p>
                    </div>
                  </div>
                  <i className={`bi bi-arrow-up-right text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${
                    stat.color === 'danger' ? 'text-gray-400' : 'text-white/70'
                  }`}></i>
                </div>

                <div className="mb-4 sm:mb-6 laptop:mb-5">
                  <h3 className="text-lg sm:text-xl lg:text-2xl laptop:text-3xl font-bold" style={{
                    color: stat.color === 'danger' ? '#111827' : 'var(--primary-text, #ffffff)'
                  }}
                  >
                    {stat.value}
                  </h3>
                </div>

                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between laptop:flex-row laptop:items-center laptop:justify-between gap-2 sm:gap-3 laptop:gap-4">
                  <div className="flex items-center">
                    <div className={`flex items-center px-2 py-1 laptop:px-3 laptop:py-1.5 rounded-full text-xs laptop:text-xs font-medium ${
                      stat.color === 'danger'
                        ? 'bg-gray-100 text-gray-700'
                        : 'bg-white/20 text-white'
                    }`}
                    >
                      <i className={`bi ${
                        stat.changeType === 'positive'
                          ? stat.title.toLowerCase().includes('overdue') ? 'bi-check-circle' : 'bi-arrow-up'
                          : 'bi-exclamation-triangle'
                      } mr-1 laptop:mr-1.5`}
                      >
                      </i>
                      <span className="whitespace-nowrap">{stat.change}</span>
                    </div>
                  </div>
                  <div className="flex-shrink-0 min-w-0">
                    <p className="text-xs laptop:text-xs text-right sm:text-left laptop:text-right break-words" style={{
                      color: stat.color === 'danger' ? '#6b7280' : 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.7)'
                    }}
                    title={stat.subtitle} // Add tooltip for full text
                    >
                      {stat.subtitle}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>

        {/* Status and Priority Distribution Charts */}
        <div className="grid grid-cols-1 laptop:grid-cols-3 xl:grid-cols-3 gap-4 sm:gap-6 laptop:gap-6 lg:gap-8 mb-6 sm:mb-8">
          {/* Status Distribution */}
          <Card className="bg-white border-0 shadow-lg">
            <CardHeader className="border-b border-gray-200 px-6 py-4">
              <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Service status overview</h5>
            </CardHeader>
            <CardBody className="p-6">
              <StatusChart
                data={getStatusDistribution(allServiceCalls)}
                type="status"
                size="sm"
                showLabels={false}
              />
            </CardBody>
          </Card>

          {/* Priority Distribution */}
          <Card className="bg-white border-0 shadow-lg">
            <CardHeader className="border-b border-gray-200 px-6 py-4">
              <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Priority breakdown</h5>
            </CardHeader>
            <CardBody className="p-6">
              <StatusChart
                data={getPriorityDistribution(allServiceCalls)}
                type="priority"
                size="sm"
                showLabels={false}
              />
            </CardBody>
          </Card>

          {/* Executive Summary */}
          <Card className="bg-white border-0 shadow-lg">
            <CardHeader className="border-b border-gray-200 px-6 py-4">
              <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Team workload</h5>
            </CardHeader>
            <CardBody className="p-6">
              <ExecutiveSummary workloadData={getExecutiveWorkload(allServiceCalls)} />
            </CardBody>
          </Card>
        </div>

        {/* Recent Customers and Service Calls Section */}

        {/* Unified Search Bar */}
        <div className="mb-6 sm:mb-8">
          <Card className="bg-white border-0 shadow-lg">
            <CardBody className="p-4 sm:p-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search customers, services, executives, and more..."
                  value={unifiedSearchTerm}
                  onChange={(e) => setUnifiedSearchTerm(e.target.value)}
                  className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                />
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <i className="bi bi-search text-gray-400 text-lg"></i>
                </div>
                {unifiedSearchLoading && (
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <Spinner size="sm" />
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Unified Search Results or Recent Data */}
        {unifiedSearchTerm ? (
          <div className="mb-6 sm:mb-8">
            <Card className="bg-white border-0 shadow-lg">
              <CardHeader className="border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">
                    Search Results ({unifiedSearchResults.length})
                  </h5>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setUnifiedSearchTerm('')}
                    className="text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-all duration-200"
                  >
                    <i className="bi bi-x mr-1"></i>
                    Clear search
                  </Button>
                </div>
              </CardHeader>
              <CardBody className="p-0">
                <div className="overflow-x-auto">
                  <Table className="mb-0 text-xs sm:text-sm laptop:text-sm">
                    <TableHead className="bg-gray-50">
                      <TableRow>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">Type</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">Title</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3 hidden sm:table-cell laptop:table-cell">Details</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">Status</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3 hidden md:table-cell laptop:table-cell">Created</TableHeader>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {unifiedSearchResults.length > 0 ? (
                        unifiedSearchResults.map((result, index) => (
                          <TableRow
                            key={`${result.type}-${result.id}-${index}`}
                            className="hover:bg-blue-50 hover:shadow-sm transition-all duration-200 border-b border-gray-100 cursor-pointer group"
                            onClick={() => {
                              if (result.type === 'customer') {
                                navigate(`/customers/${result.id}`);
                              } else if (result.type === 'service') {
                                navigate(`/services/${result.id}`);
                              } else if (result.type === 'executive') {
                                navigate(`/executives/${result.id}`);
                              }
                            }}
                          >
                            <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">
                              <Badge
                                variant={
                                  result.type === 'customer' ? 'info' :
                                  result.type === 'service' ? 'warning' :
                                  'success'
                                }
                                className="text-xs"
                              >
                                {result.type === 'customer' ? 'Customer' :
                                 result.type === 'service' ? 'Service' :
                                 'Executive'}
                              </Badge>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">
                              <div className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                                {result.title}
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                {result.subtitle}
                              </div>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3 hidden sm:table-cell laptop:table-cell">
                              <div className="text-xs text-gray-600 truncate max-w-48">
                                {result.description}
                              </div>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">
                              <Badge
                                variant={result.status === 'Active' || result.status === 'Open' ? 'success' : 'secondary'}
                                className="text-xs"
                              >
                                {result.status}
                              </Badge>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3 hidden md:table-cell laptop:table-cell">
                              <div className="text-xs text-gray-500">
                                {result.createdAt ? formatRelativeDate(result.createdAt) : 'N/A'}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="px-6 py-8 text-center">
                            <div className="text-gray-500">
                              <i className="bi bi-search text-2xl mb-2 block"></i>
                              <p className="text-sm">No results found for "{unifiedSearchTerm}"</p>
                              <p className="text-xs text-gray-400 mt-1">Try different keywords or check spelling</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardBody>
            </Card>
          </div>
        ) : (
          <div className="grid grid-cols-1 laptop:grid-cols-2 xl:grid-cols-2 gap-4 sm:gap-6 laptop:gap-6 lg:gap-8 mb-6 sm:mb-8">
          {/* Recent Customers */}
          <Card className="bg-white border-0 shadow-lg">
            <CardHeader className="border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between mb-3">
                <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">
                  Recent customers
                </h5>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/customers')}
                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-all duration-200"
                >
                  <i className="bi bi-arrow-right mr-1"></i>
                  View all customers
                </Button>
              </div>
            </CardHeader>
            <CardBody className="p-0">
              <div className="overflow-x-auto">
                <Table className="mb-0 text-xs sm:text-sm laptop:text-sm">
                  <TableHead className="bg-gray-50">
                    <TableRow>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">Name</TableHeader>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3 hidden sm:table-cell laptop:table-cell">Email</TableHeader>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">Phone</TableHeader>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3 hidden md:table-cell laptop:table-cell">Created</TableHeader>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {/* Task 5: Show recent customers */}
                    {recentCustomers.length > 0 ? (
                      consolidateCustomerInfo(recentCustomers).slice(0, 5).map((customer, index) => (
                        <TableRow
                          key={customer.id || index}
                          className="hover:bg-blue-50 hover:shadow-sm transition-all duration-200 border-b border-gray-100 cursor-pointer group"
                          onClick={() => navigate(`/customers/${customer.id}`)}
                        >
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3">
                            <div className="flex items-center">
                              <div className="w-6 h-6 sm:w-8 sm:h-8 laptop:w-8 laptop:h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2 sm:mr-3 laptop:mr-3 flex-shrink-0">
                                <span className="text-blue-600 font-medium text-xs sm:text-sm laptop:text-sm">
                                  {customer.initials}
                                </span>
                              </div>
                              <div className="flex flex-col">
                                <span className="font-medium text-gray-900 group-hover:text-blue-700 text-xs sm:text-sm laptop:text-sm truncate transition-colors">
                                  {customer.displayName}
                                </span>
                                <span className="text-gray-500 text-xs truncate">
                                  {customer.primaryContact}
                                </span>
                              </div>
                              <i className="bi bi-arrow-right text-gray-400 group-hover:text-blue-600 opacity-0 group-hover:opacity-100 transition-all duration-200 ml-2"></i>
                            </div>
                          </TableCell>
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3 text-gray-600 text-xs sm:text-sm laptop:text-sm hidden sm:table-cell laptop:table-cell">{customer.email || 'No email'}</TableCell>
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3 text-gray-600 text-xs sm:text-sm laptop:text-sm">{customer.phone || 'No phone'}</TableCell>
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3 text-gray-600 text-xs sm:text-sm laptop:text-sm hidden md:table-cell laptop:table-cell">{formatRelativeDate(customer.created_at)}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} className="p-0">
                          <CustomersEmptyState size="sm" />
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardBody>
          </Card>

          {/* Recent Service Calls */}
          <Card className="bg-white border-0 shadow-lg">
            <CardHeader className="border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between mb-3">
                <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">
                  Recent service calls
                </h5>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/services')}
                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-all duration-200"
                >
                  <i className="bi bi-arrow-right mr-1"></i>
                  View all services
                </Button>
              </div>
            </CardHeader>
            <CardBody className="p-0">
              <div className="overflow-x-auto">
                <Table className="mb-0 text-xs sm:text-sm laptop:text-sm">
                  <TableHead className="bg-gray-50">
                    <TableRow>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">Call #</TableHeader>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">Customer</TableHeader>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3 hidden md:table-cell laptop:table-cell">Subject</TableHeader>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3 hidden sm:table-cell laptop:table-cell">Assigned to</TableHeader>
                      <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs laptop:text-xs tracking-wide px-3 sm:px-6 laptop:px-4 py-2 sm:py-3 laptop:py-3">Status</TableHeader>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {/* Task 5: Show recent service calls */}
                    {recentServiceCalls.length > 0 ? (
                      consolidateServiceCallDescriptions(recentServiceCalls).slice(0, 5).map((call, index) => (
                        <TableRow
                          key={call.id || index}
                          className="hover:bg-blue-50 hover:shadow-sm transition-all duration-200 border-b border-gray-100 cursor-pointer group"
                          onClick={() => navigate(`/services/${call.id}`)}
                        >
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3">
                            <div className="flex items-center justify-between w-full">
                              <div className="flex flex-col">
                                <span className="font-medium text-gray-900 group-hover:text-blue-700 text-xs sm:text-sm laptop:text-sm transition-colors">
                                  {call.call_number || `SC-${index + 1}`}
                                </span>
                                <span className="text-gray-500 text-xs">
                                  {formatRelativeDate(call.created_at)}
                                </span>
                              </div>
                              <i className="bi bi-arrow-right text-gray-400 group-hover:text-blue-600 opacity-0 group-hover:opacity-100 transition-all duration-200"></i>
                            </div>
                          </TableCell>
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3 text-gray-600 text-xs sm:text-sm laptop:text-sm">
                            <div className="flex items-center space-x-2">
                              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-blue-600 font-medium text-xs">
                                  {(call.customer?.company_name || call.customer?.display_name || 'U').charAt(0).toUpperCase()}
                                </span>
                              </div>
                              <span className="truncate block max-w-[120px] sm:max-w-none laptop:max-w-[140px]">
                                {call.customer?.company_name || call.customer?.display_name || 'Unknown Customer'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3 text-gray-600 text-xs sm:text-sm laptop:text-sm hidden md:table-cell laptop:table-cell">
                            <span className="truncate block max-w-[150px] laptop:max-w-[160px]" title={call.consolidatedSubject}>
                              {call.consolidatedSubject || 'General support'}
                            </span>
                          </TableCell>
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3 hidden sm:table-cell laptop:table-cell">
                            <div className="flex items-center space-x-2">
                              <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-gray-600 font-medium text-xs">
                                  {(call.assigned_to?.name || call.executive?.name || 'U').charAt(0).toUpperCase()}
                                </span>
                              </div>
                              <span className="text-xs text-gray-600 truncate max-w-[80px]" title={call.assigned_to?.name || call.executive?.name || 'Unassigned'}>
                                {call.assigned_to?.name || call.executive?.name || 'Unassigned'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="px-3 sm:px-6 laptop:px-4 py-2 sm:py-4 laptop:py-3">
                            <ServiceStatusBadge status={call.status?.name || 'Open'} size="sm" />
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="p-0">
                          <ServiceCallsEmptyState size="sm" />
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardBody>
          </Card>


          </div>
        )}

        {/* Quick Actions */}
        <Card className="border-0 shadow-lg mt-6 bg-gray-50">
          <div className="bg-white border-b border-gray-200 px-6 py-4 laptop:px-6 laptop:py-4">
            <h5 className="text-base sm:text-lg laptop:text-lg font-semibold text-gray-900 mb-0">Quick actions</h5>
          </div>
          <div className="p-6 laptop:p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 laptop:grid-cols-4 lg:grid-cols-4 gap-3 sm:gap-4 laptop:gap-4">
              <Button
                className="w-full btn-primary border-0 shadow-md hover:shadow-lg transition-all duration-200 laptop:py-3 laptop:text-sm"
                onClick={() => navigate('/customers/add')}
              >
                <i className="bi bi-person-plus mr-2 laptop:mr-2"></i>
                Add new customer
              </Button>
              <Button
                variant="outline"
                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 laptop:py-3 laptop:text-sm"
                onClick={() => navigate('/services/add')}
              >
                <i className="bi bi-tools mr-2 laptop:mr-2"></i>
                Create service call
              </Button>
              <Button
                variant="outline"
                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 laptop:py-3 laptop:text-sm"
                onClick={() => navigate('/sales/add')}
              >
                <i className="bi bi-graph-up mr-2 laptop:mr-2"></i>
                Record sale
              </Button>
              <Button
                variant="outline"
                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 laptop:py-3 laptop:text-sm"
                onClick={() => navigate('/reports')}
              >
                <i className="bi bi-file-earmark-text mr-2 laptop:mr-2"></i>
                Generate report
              </Button>
            </div>
          </div>
        </Card>
          </>
        ) : (
          /* Analytics Tab */
          <AnalyticsDashboard period="30d" />
        )}

      </div>
    </>
  );
};

export default Dashboard;
