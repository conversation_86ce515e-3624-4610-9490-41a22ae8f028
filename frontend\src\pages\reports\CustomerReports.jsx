import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import api from '../../services/api';
import { useClearFiltersListener } from '../../utils/scrollPreservation';
import {
  FaUsers,
  FaArrowLeft,
  FaDownload,
  FaFilter,
  FaSearch,
  FaEye,
  FaBuilding,
  FaMapMarkerAlt,
  FaUserTie,
  FaEnvelope,
  FaPhone,
  FaCalendarAlt,
  FaChartBar,
  FaTable,
  FaTh,
  FaCalendar,
  FaCheckCircle,
  FaTimes
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const CustomerReports = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    limit: 50,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    industryId: '',
    areaId: '',
    assignedExecutiveId: '',
    searchTerm: '',
    profileStatus: '',
    joinedDateFrom: '',
    joinedDateTo: '',
    page: 1,
    limit: 50
  });
  const [executives, setExecutives] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'card'

  useEffect(() => {
    fetchReportData();
    fetchExecutives();
  }, []);

  const fetchExecutives = async () => {
    try {
      const response = await api.get('/executives', {
        params: {
          isActive: true,
          limit: 100,
          sortBy: 'first_name',
          sortOrder: 'ASC'
        }
      });
      setExecutives(response.data.data.executives || []);
    } catch (error) {
      console.error('Error fetching executives:', error);
    }
  };

  const fetchReportData = async (pageNumber = filters.page) => {
    try {
      setLoading(true);

      // Preserve scroll position during search operations
      const currentScrollY = window.scrollY;
      const isSearchOperation = filters.searchTerm && filters.searchTerm.trim().length > 0;

      const params = new URLSearchParams();

      Object.entries({...filters, page: pageNumber}).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await api.get(`/reports/customers?${params}`);
      setReportData(response.data.data);
      if (response.data.data.pagination) {
        setPagination(response.data.data.pagination);
      }
      toast.success('Customer reports loaded successfully');

      // Restore scroll position after search operations
      if (isSearchOperation) {
        setTimeout(() => {
          window.scrollTo(0, currentScrollY);
        }, 0);
      }
    } catch (error) {
      console.error('Error fetching customer reports:', error);
      toast.error('Failed to load customer reports');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handleApplyFilters = () => {
    fetchReportData(1);
    setShowFilters(false);
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
    fetchReportData(newPage);
  };

  // Add clear filters listener
  const clearAllFilters = () => {
    setFilters({
      dateFrom: '',
      dateTo: '',
      industryId: '',
      areaId: '',
      assignedExecutiveId: '',
      searchTerm: '',
      profileStatus: '',
      joinedDateFrom: '',
      joinedDateTo: '',
      page: 1,
      limit: 50
    });
  };

  useClearFiltersListener(clearAllFilters);

  // Add search debouncing with useEffect
  useEffect(() => {
    if (filters.searchTerm !== undefined) { // Only trigger after initial load
      const timeoutId = setTimeout(() => {
        fetchReportData(1);
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [filters.searchTerm]);

  // Add filter change effect for quick filters
  useEffect(() => {
    const hasActiveFilters = filters.dateFrom || filters.dateTo || filters.profileStatus;
    if (hasActiveFilters) {
      const timeoutId = setTimeout(() => {
        fetchReportData(1);
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [filters.dateFrom, filters.dateTo, filters.profileStatus]);

  const handleSearch = (searchValue) => {
    setFilters(prev => ({
      ...prev,
      searchTerm: searchValue,
      page: 1
    }));
  };

  const handleExport = async (format = 'csv') => {
    try {
      toast.loading(`Exporting customer report as ${format.toUpperCase()}...`);

      // Use the comprehensive export API instead of limited report data
      const response = await api.get('/customers/export', {
        params: {
          format: 'csv',
          // Add current filters to the export
          ...filters,
        },
        responseType: 'blob',
      });

      // Create download link
      const blob = new Blob([response.data], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `customer_report_${timestamp}.csv`;
      link.setAttribute('download', filename);

      // Trigger download
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      toast.success(`Exported customer report successfully to ${filename}`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export customer report');
    }
  };

  // Use the customers directly from backend since search is handled server-side
  const filteredCustomers = reportData?.customers || [];

  const renderSummaryCards = () => {
    if (!reportData?.summary) return null;

    const summary = reportData.summary;
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Total Customers</p>
                <p className="text-3xl font-bold text-white">{summary.totalCustomers || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaUsers size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Active Customers</p>
                <p className="text-3xl font-bold text-white">{summary.activeCustomers || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaBuilding size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Prospects</p>
                <p className="text-3xl font-bold text-white">{summary.prospects || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaUserTie size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">This Month</p>
                <p className="text-3xl font-bold text-white">{summary.newThisMonth || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaCalendarAlt size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderCardView = () => {
    if (!reportData?.customers || reportData.customers.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
            <FaUsers className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
          <p className="text-gray-600 mb-4">
            {filters.searchTerm ? 'Try adjusting your search criteria.' : 'No customer data available.'}
          </p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {reportData.customers.map((customer, index) => (
          <div key={customer.id || index} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="p-6">
              {/* Customer Header */}
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full flex items-center justify-center mr-3 flex-shrink-0" style={{ backgroundColor: '#15579e' }}>
                  <span className="text-white font-medium text-lg">
                    {customer.companyName ? customer.companyName.charAt(0).toUpperCase() : 'C'}
                  </span>
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {customer.companyName || 'Unknown Company'}
                  </h3>
                  <p className="text-xs text-gray-500 truncate">
                    {customer.customerCode || 'N/A'}
                  </p>
                </div>
              </div>

              {/* Customer Details */}
              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <FaUserTie className="mr-2 text-gray-400" size={12} />
                  <span className="truncate">{customer.contactPerson || 'N/A'}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <FaEnvelope className="mr-2 text-gray-400" size={12} />
                  <span className="truncate">{customer.email || 'N/A'}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <FaPhone className="mr-2 text-gray-400" size={12} />
                  <span className="truncate">{customer.phone || 'N/A'}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <FaMapMarkerAlt className="mr-2 text-gray-400" size={12} />
                  <span className="truncate">{customer.location || 'N/A'}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <FaBuilding className="mr-2 text-gray-400" size={12} />
                  <span className="truncate">{customer.industry || 'N/A'}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <FaUserTie className="mr-2 text-gray-400" size={12} />
                  <span className="truncate">{customer.assignedExecutive?.name || 'Unassigned'}</span>
                </div>
              </div>

              {/* Status and Actions */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex items-center justify-between">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    customer.profileStatus === 'active'
                      ? 'bg-green-100 text-green-800'
                      : customer.profileStatus === 'inactive'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {customer.profileStatus === 'active' ? 'Active' :
                     customer.profileStatus === 'inactive' ? 'Inactive' :
                     customer.customerType || 'Prospect'}
                  </span>
                  <button
                    className="text-indigo-600 hover:text-indigo-900 flex items-center text-sm"
                    onClick={() => navigate(`/customers/${customer.id}`)}
                  >
                    <FaEye className="mr-1" size={12} />
                    View
                  </button>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  Joined: {new Date(customer.createdAt).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Customer Reports..."
        subtitle="Fetching comprehensive customer analytics and data"
        variant="page"
      />
    );
  }

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/reports')}
            className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <FaArrowLeft size={20} />
          </button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-0">Customer Reports</h2>
            <p className="text-gray-600">Comprehensive customer analytics and insights</p>
          </div>
        </div>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          {/* View Toggle */}
          <div className="flex border rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('table')}
              className={`px-3 py-2 text-xs sm:text-sm font-medium transition-colors ${
                viewMode === 'table'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FaTable className="mr-1" />
              Table
            </button>
            <button
              onClick={() => setViewMode('card')}
              className={`px-3 py-2 text-xs sm:text-sm font-medium transition-colors ${
                viewMode === 'card'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FaTh className="mr-1" />
              Cards
            </button>
          </div>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
            style={{
              borderColor: '#15579e',
              color: '#15579e'
            }}
          >
            <FaFilter className="mr-1 sm:mr-2" />
            Filters
          </button>
          <button
            onClick={() => handleExport('csv')}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white flex-1 sm:flex-none justify-center"
            style={{
              backgroundColor: '#15579e',
              borderColor: '#15579e'
            }}
          >
            <FaDownload className="mr-1 sm:mr-2" />
            Export CSV
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {renderSummaryCards()}

      {/* Filters Panel */}
      {showFilters && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter Options</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date From</label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date To</label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Profile Status</label>
              <select
                value={filters.profileStatus}
                onChange={(e) => handleFilterChange('profileStatus', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Status</option>
                <option value="active">Active Users</option>
                <option value="inactive">Inactive Users</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Joined Date From</label>
              <input
                type="date"
                value={filters.joinedDateFrom}
                onChange={(e) => handleFilterChange('joinedDateFrom', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Joined Date To</label>
              <input
                type="date"
                value={filters.joinedDateTo}
                onChange={(e) => handleFilterChange('joinedDateTo', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Assigned Executive</label>
              <select
                value={filters.assignedExecutiveId}
                onChange={(e) => handleFilterChange('assignedExecutiveId', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Executives</option>
                {executives.map(executive => (
                  <option key={executive.id} value={executive.id}>
                    {executive.first_name} {executive.last_name} ({executive.employee_code})
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="flex justify-end mt-4 gap-2">
            <button
              onClick={() => setShowFilters(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilters}
              className="px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
              style={{ backgroundColor: '#15579e' }}
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {/* Search and Quick Filters */}
      <div className="mb-6">
        {/* Search Bar */}
        <div className="relative mb-4">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 text-sm"
            placeholder="Search customers by name, code, email, tally serial number, or executive name..."
            value={filters.searchTerm}
            onChange={(e) => {
              handleFilterChange('searchTerm', e.target.value);
              handleSearch(e.target.value);
            }}
          />
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap items-center gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          {/* Date Filter */}
          <div className="flex items-center gap-2">
            <FaCalendar className="text-gray-400" size={14} />
            <label className="text-sm font-medium text-gray-700">Date:</label>
            <select
              value={filters.dateFilter || ''}
              onChange={(e) => {
                const selectedFilter = e.target.value;
                handleFilterChange('dateFilter', selectedFilter);

                // Set date range based on selection
                const today = new Date();
                let dateFrom = '';
                let dateTo = '';

                switch (selectedFilter) {
                  case 'today':
                    dateFrom = dateTo = today.toISOString().split('T')[0];
                    break;
                  case 'yesterday':
                    const yesterday = new Date(today);
                    yesterday.setDate(today.getDate() - 1);
                    dateFrom = dateTo = yesterday.toISOString().split('T')[0];
                    break;
                  case 'thisMonth':
                    dateFrom = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                    dateTo = today.toISOString().split('T')[0];
                    break;
                  case 'lastMonth':
                    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                    dateFrom = lastMonth.toISOString().split('T')[0];
                    dateTo = lastMonthEnd.toISOString().split('T')[0];
                    break;
                  case 'thisYear':
                    dateFrom = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                    dateTo = today.toISOString().split('T')[0];
                    break;
                  default:
                    dateFrom = dateTo = '';
                }

                handleFilterChange('dateFrom', dateFrom);
                handleFilterChange('dateTo', dateTo);
              }}
              className="text-sm border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white min-w-[120px]"
            >
              <option value="">All Dates</option>
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="thisMonth">This Month</option>
              <option value="lastMonth">Last Month</option>
              <option value="thisYear">This Year</option>
            </select>
          </div>



          {/* Profile Status Filter */}
          <div className="flex items-center gap-2">
            <FaCheckCircle className="text-gray-400" size={14} />
            <label className="text-sm font-medium text-gray-700">Status:</label>
            <select
              value={filters.profileStatus || ''}
              onChange={(e) => handleFilterChange('profileStatus', e.target.value)}
              className="text-sm border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white min-w-[100px]"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          {/* Clear Filters Button */}
          {(filters.dateFilter || filters.profileStatus || filters.searchTerm) && (
            <button
              onClick={() => {
                setFilters({
                  page: 1,
                  limit: 10,
                  searchTerm: '',
                  dateFrom: '',
                  dateTo: '',
                  profileStatus: '',
                  dateFilter: '',
                  joinedDateFrom: '',
                  joinedDateTo: '',
                  assignedExecutiveId: ''
                });
                fetchReportData(1);
              }}
              className="flex items-center gap-1 px-3 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded border border-red-200 transition-colors"
            >
              <FaTimes size={12} />
              Clear Filters
            </button>
          )}
        </div>
      </div>

      {/* Customer Data */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">
              Customer List ({pagination.totalRecords} total records)
            </h3>
            <div className="flex items-center text-sm text-gray-500">
              <FaChartBar className="mr-2" />
              Real-time data
            </div>
          </div>
        </div>

        {viewMode === 'table' ? (
          reportData?.customers && reportData.customers.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact Information
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Business Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.customers.map((customer, index) => (
                  <tr key={customer.id || index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3 flex-shrink-0" style={{ backgroundColor: '#15579e' }}>
                          <span className="text-white font-medium text-sm">
                            {customer.companyName ? customer.companyName.charAt(0).toUpperCase() : 'C'}
                          </span>
                        </div>
                        <div className="min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {customer.companyName || 'Unknown Company'}
                          </div>
                          <div className="text-sm text-gray-500 truncate">
                            Code: {customer.customerCode || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="flex items-center mb-1">
                          <FaUserTie className="mr-2 text-gray-400" size={12} />
                          {customer.contactPerson || 'N/A'}
                        </div>
                        <div className="flex items-center mb-1">
                          <FaEnvelope className="mr-2 text-gray-400" size={12} />
                          {customer.email || 'N/A'}
                        </div>
                        <div className="flex items-center">
                          <FaPhone className="mr-2 text-gray-400" size={12} />
                          {customer.phone || 'N/A'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="flex items-center mb-1">
                          <FaMapMarkerAlt className="mr-2 text-gray-400" size={12} />
                          {customer.location || 'N/A'}
                        </div>
                        <div className="flex items-center mb-1">
                          <FaBuilding className="mr-2 text-gray-400" size={12} />
                          {customer.industry || 'N/A'}
                        </div>
                        <div className="flex items-center">
                          <FaUserTie className="mr-2 text-gray-400" size={12} />
                          {customer.assignedExecutive?.name || 'Unassigned'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          customer.profileStatus === 'active'
                            ? 'bg-green-100 text-green-800'
                            : customer.profileStatus === 'inactive'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {customer.profileStatus === 'active' ? 'Active' :
                           customer.profileStatus === 'inactive' ? 'Inactive' :
                           customer.customerType || 'Prospect'}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(customer.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        className="text-indigo-600 hover:text-indigo-900 flex items-center"
                        onClick={() => navigate(`/customers/${customer.id}`)}
                      >
                        <FaEye className="mr-1" size={12} />
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          ) : (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                <FaUsers className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
              <p className="text-gray-600 mb-4">
                {filters.searchTerm ? 'Try adjusting your search criteria.' : 'No customer data available.'}
              </p>
            </div>
          )
        ) : (
          <div className="p-6">
            {renderCardView()}
          </div>
        )}

        {/* Pagination */}
        {reportData?.customers && reportData.customers.length > 0 && pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((pagination.currentPage - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.currentPage * pagination.limit, pagination.totalRecords)} of{' '}
                {pagination.totalRecords} results
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={!pagination.hasPrevPage}
                  className="px-3 py-1 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                >
                  Previous
                </button>

                {/* Page numbers */}
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.currentPage >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = pagination.currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`px-3 py-1 text-sm border rounded-md ${
                          pageNum === pagination.currentPage
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'hover:bg-gray-100'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="px-3 py-1 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerReports;
