import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import {
  FaPlus,
  FaEdit,
  FaEye,
  FaTrash,
  FaSearch,
  FaFilter,
  FaDownload,
  FaRupeeSign,
  FaUser,
  FaCalendar,
  FaChartLine,
  FaTrophy,
  FaHandshake,
  FaTh,
  FaList,
  FaEllipsisV
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';
import { Card, CardBody } from '../../components/ui/Card';

const SalesList = () => {
  const navigate = useNavigate();
  const [sales, setSales] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterStage, setFilterStage] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [salesPerPage] = useState(10);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'card'
  const [dropdownOpen, setDropdownOpen] = useState({});

  // Force card view on mobile devices
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) { // sm breakpoint
        setViewMode('card');
      }
    };

    // Check on mount
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch sales data from API - Combined useEffect with proper dependencies
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Preserve scroll position during search
      const currentScrollY = window.scrollY;
      const isSearchOperation = searchTerm && searchTerm.trim().length > 0;

      fetchSalesData().finally(() => {
        // Restore scroll position after data fetch (only for search operations)
        if (isSearchOperation) {
          setTimeout(() => {
            window.scrollTo(0, currentScrollY);
          }, 0);
        }
      });
    }, searchTerm ? 500 : 0); // Debounce only for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filterStatus, filterStage, currentPage]); // Removed duplicate useEffect

  const fetchSalesData = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/sales', {
        params: {
          page: currentPage,
          limit: salesPerPage,
          ...(searchTerm && { search: searchTerm }),
          ...(filterStatus !== 'all' && { status: filterStatus }),
          ...(filterStage !== 'all' && { stage: filterStage }),
        }
      });

      if (response.data?.success) {
        const salesData = response.data.data.sales || [];
        // Transform API data to match frontend expectations
        const transformedSales = salesData.map(sale => ({
          id: sale.id,
          leadNumber: sale.saleNumber || `SAL-${sale.id}`,
          customer: sale.customer?.company_name || sale.customer?.display_name || 'Unknown Customer',
          customerId: sale.customer?.id,
          contactPerson: sale.customer?.contact_person || 'N/A',
          email: sale.customer?.email || 'N/A',
          phone: sale.customer?.phone || 'N/A',
          product: 'Tally Product', // This would come from sale items
          productType: 'Software License',
          stage: sale.status === 'draft' ? 'lead' :
            sale.status === 'confirmed' ? 'closed-won' :
              sale.paymentStatus === 'paid' ? 'closed-won' : 'proposal',
          status: sale.status === 'confirmed' && sale.paymentStatus === 'paid' ? 'won' :
            sale.status === 'cancelled' ? 'lost' : 'active',
          priority: 'medium', // This would need to be added to the Sale model
          source: 'Direct', // This would need to be added to the Sale model
          assignedTo: sale.salesExecutive?.name || 'Unassigned',
          expectedValue: sale.totalAmount || 0,
          probability: sale.status === 'confirmed' ? 100 :
            sale.paymentStatus === 'paid' ? 100 : 50,
          expectedCloseDate: sale.saleDate || sale.createdAt,
          createdDate: sale.createdAt,
          lastActivity: sale.updatedAt,
          notes: sale.description || 'No notes available'
        }));

        setSales(transformedSales);
      } else {
        console.error('Failed to fetch sales data:', response.data?.message);
        setSales([]);
      }
    } catch (error) {
      console.error('Error fetching sales data:', error);
      toast.error('Failed to load sales data');
      setSales([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter sales based on search, status, and stage
  const filteredSales = sales.filter(sale => {
    const matchesSearch = sale.leadNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.product.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || sale.status === filterStatus;
    const matchesStage = filterStage === 'all' || sale.stage === filterStage;
    return matchesSearch && matchesStatus && matchesStage;
  });

  // Pagination
  const indexOfLastSale = currentPage * salesPerPage;
  const indexOfFirstSale = indexOfLastSale - salesPerPage;
  const currentSales = filteredSales.slice(indexOfFirstSale, indexOfLastSale);
  const totalPages = Math.ceil(filteredSales.length / salesPerPage);

  // Calculate stats
  const totalValue = sales.reduce((sum, sale) => sum + sale.expectedValue, 0);
  const wonDeals = sales.filter(sale => sale.status === 'won');
  const activeDeals = sales.filter(sale => sale.status === 'active');
  const avgDealSize = sales.length > 0 ? totalValue / sales.length : 0;

  // Toggle dropdown for actions
  const toggleDropdown = (saleId) => {
    setDropdownOpen(prev => ({
      ...prev,
      [saleId]: !prev[saleId]
    }));
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setDropdownOpen({});
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const handleDelete = async (saleId) => {
    if (window.confirm('Are you sure you want to delete this sales opportunity?')) {
      try {
        const response = await apiService.delete(`/sales/${saleId}`);
        if (response.data?.success) {
          setSales(sales.filter(sale => sale.id !== saleId));
          toast.success('Sales opportunity deleted successfully');
        } else {
          toast.error(response.data?.message || 'Failed to delete sales opportunity');
        }
      } catch (error) {
        console.error('Error deleting sale:', error);
        toast.error('Failed to delete sales opportunity');
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'won': { bg: 'bg-success-100', text: 'text-success-800', icon: '🏆' },
      'lost': { bg: 'bg-danger-100', text: 'text-danger-800', icon: '❌' },
      'active': { bg: 'bg-theme-100', text: 'text-theme-800', icon: '🔄' }
    };

    const config = statusConfig[status] || statusConfig.active;
    const displayText = status.charAt(0).toUpperCase() + status.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const getStageBadge = (stage) => {
    const stageConfig = {
      'lead': { bg: 'bg-secondary-100', text: 'text-secondary-800', icon: '🎯' },
      'qualification': { bg: 'bg-theme-100', text: 'text-theme-800', icon: '🔍' },
      'proposal': { bg: 'bg-warning-100', text: 'text-warning-800', icon: '📋' },
      'negotiation': { bg: 'badge-primary', text: '', icon: '🤝' },
      'closed-won': { bg: 'bg-success-100', text: 'text-success-800', icon: '✅' },
      'closed-lost': { bg: 'bg-danger-100', text: 'text-danger-800', icon: '❌' }
    };

    const config = stageConfig[stage] || stageConfig.lead;
    const displayText = stage.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      'high': { bg: 'bg-danger-100', text: 'text-danger-800', icon: '🔥' },
      'medium': { bg: 'bg-warning-100', text: 'text-warning-800', icon: '⚡' },
      'low': { bg: 'bg-success-100', text: 'text-success-800', icon: '🌱' }
    };

    const config = priorityConfig[priority] || priorityConfig.medium;
    const displayText = priority.charAt(0).toUpperCase() + priority.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const handleExport = () => {
    try {
      // Prepare data for export
      const exportData = filteredSales.map(sale => ({
        'Lead Number': sale.leadNumber,
        'Customer': sale.customer,
        'Contact Person': sale.contactPerson,
        'Product': sale.product,
        'Product Type': sale.productType,
        'Stage': sale.stage,
        'Status': sale.status,
        'Priority': sale.priority,
        'Assigned To': sale.assignedTo,
        'Expected Value': sale.expectedValue,
        'Probability': `${sale.probability}%`,
        'Expected Close Date': new Date(sale.expectedCloseDate).toLocaleDateString()
      }));

      // Convert to CSV
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header =>
            `"${(row[header] || '').toString().replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Exported ${exportData.length} sales opportunities successfully`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export sales data');
    }
  };

  // Card View Component
  const SaleCard = ({ sale }) => (
    <Card className="hover:shadow-lg transition-all duration-200 border border-gray-200">
      <CardBody className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-blue-600 font-bold text-sm">#</span>
            </div>
            <div>
              <h6 className="font-bold text-gray-900 mb-1">{sale.leadNumber}</h6>
              <p className="text-sm text-gray-600 mb-0">{sale.customer}</p>
            </div>
          </div>
          <div className="relative">
            <button
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                toggleDropdown(sale.id);
              }}
            >
              <FaEllipsisV className="h-4 w-4" />
            </button>
            {dropdownOpen[sale.id] && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      navigate(`/sales/${sale.id}`);
                      setDropdownOpen({});
                    }}
                  >
                    <FaEye className="mr-3 h-4 w-4 text-blue-600" />
                    View Details
                  </button>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      navigate(`/sales/${sale.id}/edit`);
                      setDropdownOpen({});
                    }}
                  >
                    <FaEdit className="mr-3 h-4 w-4 text-blue-600" />
                    Edit
                  </button>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    onClick={() => {
                      handleDelete(sale.id);
                      setDropdownOpen({});
                    }}
                  >
                    <FaTrash className="mr-3 h-4 w-4" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2 mb-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Stage:</span>
            {getStageBadge(sale.stage)}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Status:</span>
            {getStatusBadge(sale.status)}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Value:</span>
            <div className="flex items-center text-green-600">
              <FaRupeeSign className="h-3 w-3 mr-1" />
              <span className="font-bold">₹{sale.expectedValue?.toLocaleString() || '0'}</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Probability:</span>
            <span className="text-sm font-medium">{sale.probability}%</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Assigned To:</span>
            <span className="text-sm font-medium">{sale.assignedTo}</span>
          </div>
        </div>
      </CardBody>
    </Card>
  );

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Sales Data..."
        subtitle="Please wait while we fetch your sales pipeline"
        variant="page"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-6 text-white">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex-1">
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 flex items-center">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mr-3 sm:mr-4" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                    <FaChartLine className="text-lg sm:text-xl" style={{ color: 'var(--primary-text, #ffffff)' }} />
                  </div>
                  <span className="hidden sm:inline">Sales Management</span>
                  <span className="sm:hidden">Sales</span>
                </h2>
                <p className="text-sm sm:text-base" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>
                  <span className="hidden sm:inline">Track leads, opportunities, and sales pipeline</span>
                  <span className="sm:hidden">Track leads & sales</span>
                </p>
              </div>
              <div className="flex flex-wrap gap-2 w-full sm:w-auto">
                <button
                  className="inline-flex items-center px-3 sm:px-6 py-2 sm:py-3 border-2 border-white border-opacity-30 text-xs sm:text-sm font-medium rounded-xl bg-white bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed text-white flex-1 sm:flex-none justify-center"
                  onClick={handleExport}
                  disabled={filteredSales.length === 0}
                >
                  <FaDownload className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Export ({filteredSales.length})</span>
                  <span className="sm:hidden">Export ({filteredSales.length})</span>
                </button>
                <Link
                  to="/sales/add"
                  className="inline-flex items-center px-3 sm:px-6 py-2 sm:py-3 border border-transparent text-xs sm:text-sm font-medium rounded-xl bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg flex-1 sm:flex-none justify-center"
                  style={{ color: '#2f69b3' }}
                >
                  <FaPlus className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">New Lead</span>
                  <span className="sm:hidden">Add Lead</span>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="stats-card-primary rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0">{sales.length}</h4>
                  <p className="mb-0 text-sm" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Total Opportunities</p>
                </div>
                <div className="rounded-lg p-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                  <FaChartLine className="h-6 w-6" style={{ color: 'var(--primary-text, #ffffff)' }} />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-success-500/20 text-success-300 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +25%
                </div>
                <span className="text-xs ml-2" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.7)' }}>vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">{wonDeals.length}</h4>
                  <p className="text-gray-600 mb-0 text-sm">Won Deals</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-3">
                  <FaTrophy className="h-6 w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-success-100 text-success-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +18%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">₹{totalValue.toLocaleString()}</h4>
                  <p className="text-gray-600 mb-0 text-sm">Pipeline Value</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-3">
                  <FaRupeeSign className="h-6 w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-success-100 text-success-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +32%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">₹{Math.round(avgDealSize).toLocaleString()}</h4>
                  <p className="text-gray-600 mb-0 text-sm">Avg Deal Size</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-3">
                  <FaHandshake className="h-6 w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-theme-100 text-theme-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +8%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last month</span>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Filters */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-8 border-2" style={{ borderColor: '#2f69b3' }}>
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
            <div className="md:col-span-4">
              <label className="block text-sm font-bold mb-2" style={{ color: '#2f69b3' }}>🔍 Search Opportunities</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FaSearch className="h-5 w-5" style={{ color: '#2f69b3' }} />
                </div>
                <input
                  type="text"
                  className="block w-full pl-12 pr-4 py-3 border-2 rounded-xl leading-5 bg-white transition-all duration-200 sm:text-sm focus:outline-none focus:ring-2"
                  style={{
                    borderColor: '#2f69b3',
                    '--tw-ring-color': '#2f69b3'
                  }}
                  placeholder="Search by lead number, customer..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="md:col-span-3">
              <label className="block text-sm font-bold mb-2" style={{ color: '#2f69b3' }}>📊 Status Filter</label>
              <select
                className="block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 bg-white transition-all duration-200 sm:text-sm"
                style={{
                  borderColor: '#2f69b3',
                  '--tw-ring-color': '#2f69b3'
                }}
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">🌟 All Status</option>
                <option value="active">🔄 Active</option>
                <option value="won">🏆 Won</option>
                <option value="lost">❌ Lost</option>
              </select>
            </div>
            <div className="md:col-span-3">
              <label className="block text-sm font-bold mb-2" style={{ color: '#2f69b3' }}>🎯 Stage Filter</label>
              <select
                className="block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 bg-white transition-all duration-200 sm:text-sm"
                style={{
                  borderColor: '#2f69b3',
                  '--tw-ring-color': '#2f69b3'
                }}
                value={filterStage}
                onChange={(e) => setFilterStage(e.target.value)}
              >
                <option value="all">🌈 All Stages</option>
                <option value="lead">🎯 Lead</option>
                <option value="qualification">🔍 Qualification</option>
                <option value="proposal">📋 Proposal</option>
                <option value="negotiation">🤝 Negotiation</option>
                <option value="closed-won">✅ Closed Won</option>
                <option value="closed-lost">❌ Closed Lost</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-bold mb-2" style={{ color: '#2f69b3' }}>👁️ View</label>
              {/* View Mode Toggle - Hidden on mobile, only show card view */}
              <div className="hidden sm:flex gap-2">
                <button
                  className={`flex-1 inline-flex items-center justify-center px-3 py-3 text-xs font-bold border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 ${
                    viewMode === 'table'
                      ? 'border-transparent shadow-lg'
                      : 'border-gray-300 text-gray-600 bg-white hover:bg-gray-50'
                  }`}
                  style={viewMode === 'table' ? {
                    backgroundColor: '#2f69b3',
                    color: 'white',
                    '--tw-ring-color': '#2f69b3'
                  } : {
                    '--tw-ring-color': '#2f69b3'
                  }}
                  onClick={() => setViewMode('table')}
                >
                  <FaList className="h-3 w-3" />
                </button>
                <button
                  className={`flex-1 inline-flex items-center justify-center px-3 py-3 text-xs font-bold border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 ${
                    viewMode === 'card'
                      ? 'border-transparent shadow-lg'
                      : 'border-gray-300 text-gray-600 bg-white hover:bg-gray-50'
                  }`}
                  style={viewMode === 'card' ? {
                    backgroundColor: '#2f69b3',
                    color: 'white',
                    '--tw-ring-color': '#2f69b3'
                  } : {
                    '--tw-ring-color': '#2f69b3'
                  }}
                  onClick={() => setViewMode('card')}
                >
                  <FaTh className="h-3 w-3" />
                </button>
              </div>
              {/* Mobile - Force card view only */}
              <div className="sm:hidden">
                <div className="w-full inline-flex items-center justify-center px-4 py-3 border-2 rounded-xl shadow-sm text-sm font-medium bg-gray-100 text-gray-500 cursor-not-allowed"
                  style={{ borderColor: '#e5e7eb' }}
                >
                  <FaTh className="mr-2 h-4 w-4" />
                  Card View
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Conditional View Rendering */}
        {/* Table View - Hidden on mobile */}
        {viewMode === 'table' && (
          <div className="hidden sm:block">
            <div className="bg-white shadow-2xl rounded-2xl overflow-hidden border border-gray-100">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-blue-600">
                    <tr>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">🔢 Lead #</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">👤 Customer</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">📦 Product</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">🎯 Stage</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">📊 Status</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">⚡ Priority</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">👨‍💼 Assigned To</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">💰 Value</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">📈 Probability</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">📅 Close Date</th>
                      <th className="px-4 py-4 text-left text-xs font-bold uppercase tracking-wider text-white">⚙️ Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentSales.map((sale, index) => (
                      <tr key={sale.id} className={`hover:bg-gray-50 transition-all duration-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                              <span className="text-blue-600 font-bold text-xs">#</span>
                            </div>
                            <span className="text-sm font-bold text-gray-900">{sale.leadNumber}</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-bold text-gray-900">{sale.customer}</div>
                            <div className="text-xs text-gray-500">{sale.contactPerson}</div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-bold text-gray-900">{sale.product}</div>
                            <div className="text-xs text-gray-500">{sale.productType}</div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">{getStageBadge(sale.stage)}</td>
                        <td className="px-4 py-4 whitespace-nowrap">{getStatusBadge(sale.status)}</td>
                        <td className="px-4 py-4 whitespace-nowrap">{getPriorityBadge(sale.priority)}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{sale.assignedTo}</td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center text-green-600">
                            <FaRupeeSign className="h-3 w-3 mr-1" />
                            <span className="text-sm font-bold">₹{sale.expectedValue.toLocaleString()}</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${sale.probability}%` }}
                              >
                              </div>
                            </div>
                            <span className="text-sm text-gray-900">{sale.probability}%</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center text-gray-600">
                            <FaCalendar className="h-3 w-3 mr-2" />
                            <span className="text-sm">{new Date(sale.expectedCloseDate).toLocaleDateString()}</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                              onClick={() => navigate(`/sales/${sale.id}`)}
                              title="View Details"
                            >
                              <FaEye className="h-4 w-4" />
                            </button>
                            <button
                              className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                              onClick={() => navigate(`/sales/${sale.id}/edit`)}
                              title="Edit"
                            >
                              <FaEdit className="h-4 w-4" />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                              onClick={() => handleDelete(sale.id)}
                              title="Delete"
                            >
                              <FaTrash className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Card View - Always visible on mobile, toggleable on desktop */}
        {viewMode === 'card' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4 3xl:grid-cols-6 gap-4">
            {currentSales.map(sale => (
              <SaleCard key={sale.id} sale={sale} />
            ))}
          </div>
        )}

        {/* Mobile Card View - Force card view on mobile */}
        <div className="sm:hidden">
          <div className="grid grid-cols-1 gap-4">
            {currentSales.map(sale => (
              <SaleCard key={sale.id} sale={sale} />
            ))}
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              {[...Array(totalPages)].map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index + 1)}
                  className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                    currentPage === index + 1
                      ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  {index + 1}
                </button>
              ))}
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </nav>
          </div>
        )}

        {/* Empty State */}
        {currentSales.length === 0 && (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
              <FaChartLine className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No sales opportunities found</h3>
            <p className="text-gray-600 mb-4">Get started by creating your first sales opportunity.</p>
            <Link
              to="/sales/add"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <FaPlus className="mr-2 h-4 w-4" />
              Create Sales Opportunity
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default SalesList;
