/**
 * Utility functions for preserving scroll position during search operations
 * This prevents the annoying auto-scroll to top behavior during data fetching
 */
import { useEffect } from 'react';

/**
 * Preserves scroll position during an async operation
 * @param {Function} asyncOperation - The async function to execute
 * @param {boolean} shouldPreserve - Whether to preserve scroll position (default: true)
 * @returns {Promise} - The result of the async operation
 */
export const withScrollPreservation = async (asyncOperation, shouldPreserve = true) => {
  if (!shouldPreserve) {
    return await asyncOperation();
  }

  // Capture current scroll position
  const currentScrollY = window.scrollY;
  
  try {
    // Execute the async operation
    const result = await asyncOperation();
    
    // Restore scroll position after operation completes
    setTimeout(() => {
      window.scrollTo(0, currentScrollY);
    }, 0);
    
    return result;
  } catch (error) {
    // Still restore scroll position even if operation fails
    setTimeout(() => {
      window.scrollTo(0, currentScrollY);
    }, 0);
    
    throw error;
  }
};

/**
 * Hook for managing scroll preservation during search operations
 * @param {Function} fetchFunction - The data fetching function
 * @param {string} searchTerm - Current search term
 * @param {Array} dependencies - Additional dependencies for useEffect
 * @param {number} debounceMs - Debounce delay in milliseconds (default: 500)
 */
export const useScrollPreservedSearch = (fetchFunction, searchTerm, dependencies = [], debounceMs = 500) => {
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const isSearchOperation = searchTerm && searchTerm.trim().length > 0;

      if (isSearchOperation) {
        withScrollPreservation(fetchFunction, true);
      } else {
        fetchFunction();
      }
    }, searchTerm ? debounceMs : 0);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, ...dependencies]);
};

/**
 * Custom event for clearing filters without page reload
 * Components can listen to this event to clear their filters
 */
export const clearFiltersEvent = 'clearFilters';

/**
 * Dispatches a clear filters event instead of reloading the page
 */
export const dispatchClearFilters = () => {
  window.dispatchEvent(new CustomEvent(clearFiltersEvent));
};

/**
 * Hook to listen for clear filters events
 * @param {Function} clearFunction - Function to call when clear filters event is triggered
 */
export const useClearFiltersListener = (clearFunction) => {
  useEffect(() => {
    const handleClearFilters = () => {
      clearFunction();
    };

    window.addEventListener(clearFiltersEvent, handleClearFilters);

    return () => {
      window.removeEventListener(clearFiltersEvent, handleClearFilters);
    };
  }, [clearFunction]);
};

/**
 * Prevents default scroll behavior during data loading
 * Useful for maintaining user's current view position
 */
export const preventScrollDuringLoad = () => {
  const currentScrollY = window.scrollY;
  
  // Return a function to restore scroll position
  return () => {
    setTimeout(() => {
      window.scrollTo(0, currentScrollY);
    }, 0);
  };
};

/**
 * Smooth scroll to top (for intentional navigation)
 * Use this when you actually want to scroll to top (like page navigation)
 */
export const smoothScrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

/**
 * Check if current scroll position is near top
 * @param {number} threshold - Distance from top in pixels (default: 100)
 * @returns {boolean} - True if near top
 */
export const isNearTop = (threshold = 100) => {
  return window.scrollY <= threshold;
};
