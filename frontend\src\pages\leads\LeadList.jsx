import { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { leadAPI, masterDataAPI } from '../../services/api';
import themeManager from '../../utils/themeManager';
import {
  FaPlus,
  FaEdit,
  FaEye,
  FaTrash,
  FaSearch,
  FaDownload,
  FaUser,
  FaCalendar,
  FaClock,
  FaRupeeSign,
  FaTh,
  FaList,
  FaEllipsisV,
  FaPhone,
  FaBuilding,
  FaUserTie,
  FaChartLine
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';
import { Card, CardBody } from '../../components/ui/Card';
import ResponsiveTable from '../../components/ui/ResponsiveTable';
import DateRangeFilter from '../../components/DateRangeFilter';
import StandardFilterLayout, { FilterDropdown } from '../../components/common/StandardFilterLayout';
import { useViewPreference, PAGE_NAMES } from '../../utils/viewPreferences';

// Custom hook for dropdown positioning
const useDropdownPosition = () => {
  const [position, setPosition] = useState({ right: 0, top: '100%' });
  const dropdownRef = useRef(null);
  const triggerRef = useRef(null);

  const updatePosition = () => {
    if (!triggerRef.current || !dropdownRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const dropdownRect = dropdownRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let newPosition = { right: 0, top: '100%' };

    // Check if dropdown would go outside viewport on the right
    if (triggerRect.right - dropdownRect.width < 0) {
      newPosition.right = 'auto';
      newPosition.left = 0;
    }

    // Check if dropdown would go outside viewport on the bottom
    if (triggerRect.bottom + dropdownRect.height > viewportHeight) {
      newPosition.top = 'auto';
      newPosition.bottom = '100%';
    }

    setPosition(newPosition);
  };

  useEffect(() => {
    updatePosition();
    window.addEventListener('resize', updatePosition);
    window.addEventListener('scroll', updatePosition);

    return () => {
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);
    };
  }, []);

  return { position, dropdownRef, triggerRef, updatePosition };
};

const LeadList = () => {
  const navigate = useNavigate();
  const [leads, setLeads] = useState([]);
  const [leadStats, setLeadStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [availableStatuses, setAvailableStatuses] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const [currentPage, setCurrentPage] = useState(1);
  const [leadsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalLeads, setTotalLeads] = useState(0);
  const [currentThemeColor, setCurrentThemeColor] = useState('#2f69b3');
  const [viewMode, setViewMode] = useViewPreference(PAGE_NAMES.LEADS, 'table');
  const [dropdownOpen, setDropdownOpen] = useState({});
  const [dateRange, setDateRange] = useState(null);

  // Get current theme color
  useEffect(() => {
    const savedTheme = localStorage.getItem('primaryColor') || '#2f69b3';
    setCurrentThemeColor(savedTheme);
  }, []);

  // Force card view on mobile devices
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) { // sm breakpoint
        setViewMode('card');
      }
    };

    // Check on mount
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch leads data
  const fetchLeads = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: leadsPerPage,
        search: searchTerm || undefined,
        status: filterStatus !== 'all' ? filterStatus : undefined,
        // Add date range parameters
        ...(dateRange && {
          startDate: dateRange.startDate.toISOString().split('T')[0],
          endDate: dateRange.endDate.toISOString().split('T')[0]
        }),
      };

      const response = await leadAPI.getAll(params);
      
      if (response.data?.success) {
        setLeads(response.data.data.leads || []);
        setTotalPages(response.data.data.pagination?.totalPages || 1);
        setTotalLeads(response.data.data.pagination?.totalItems || 0);
      } else {
        toast.error('Failed to fetch leads');
        setLeads([]);
      }
    } catch (error) {
      console.error('Error fetching leads:', error);
      toast.error('Failed to fetch leads');
      setLeads([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch lead statistics
  const fetchLeadStats = async () => {
    try {
      setStatsLoading(true);
      const response = await leadAPI.getStats();

      if (response.data?.success) {
        setLeadStats(response.data.data || {});
      }
    } catch (error) {
      console.error('Error fetching lead stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  // Use hardcoded statuses for filter instead of fetching from masters
  const fetchAvailableStatuses = async () => {
    try {
      // Use hardcoded status options instead of fetching from masters
      const hardcodedStatuses = [
        { id: 'follow_up', name: 'Follow Up', code: 'FOLLOW_UP' },
        { id: 'call_not_attended', name: 'Call Not Attended', code: 'CALL_NOT_ATTENDED' },
        { id: 'new', name: 'New', code: 'NEW' },
        { id: 'interested', name: 'Interested', code: 'INTERESTED' },
        { id: 'not_interested', name: 'Not Interested', code: 'NOT_INTERESTED' },
        { id: 'converted', name: 'Converted', code: 'CONVERTED' },
        { id: 'lost', name: 'Lost', code: 'LOST' },
        { id: 'callback_requested', name: 'Callback Requested', code: 'CALLBACK_REQUESTED' }
      ];
      setAvailableStatuses(hardcodedStatuses);
    } catch (error) {
      console.error('Error setting hardcoded statuses:', error);
    }
  };

  // Handle date range change
  const handleDateRangeChange = (range) => {
    setDateRange(range);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Fetch leads with debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Preserve scroll position during search
      const currentScrollY = window.scrollY;
      const isSearchOperation = searchTerm && searchTerm.trim().length > 0;

      fetchLeads().finally(() => {
        // Restore scroll position after data fetch (only for search operations)
        if (isSearchOperation) {
          setTimeout(() => {
            window.scrollTo(0, currentScrollY);
          }, 0);
        }
      });
    }, searchTerm ? 500 : 0); // Debounce only for search

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filterStatus, currentPage, dateRange]);

  // Fetch initial data
  useEffect(() => {
    fetchLeadStats();
    fetchAvailableStatuses();
  }, []);

  // Toggle dropdown for actions
  const toggleDropdown = (leadId) => {
    setDropdownOpen(prev => {
      // Close all other dropdowns and toggle the current one
      const newState = {};
      newState[leadId] = !prev[leadId];
      return newState;
    });
  };

  // Close dropdown when clicking outside or pressing escape
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if click is outside any dropdown
      if (!event.target.closest('[data-dropdown-container]')) {
        setDropdownOpen({});
      }
    };

    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setDropdownOpen({});
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, []);

  const handleDelete = async (leadId) => {
    if (window.confirm('Are you sure you want to delete this lead?')) {
      try {
        const response = await leadAPI.delete(leadId);
        if (response.data?.success) {
          fetchLeads(); // Refresh the list
          fetchLeadStats(); // Refresh stats
          toast.success('Lead deleted successfully');
        } else {
          // Use the improved error message from the API response
          const errorMessage = response.data?.message || 'Failed to delete lead';
          toast.error(errorMessage);
        }
      } catch (error) {
        console.error('Error deleting lead:', error);

        // Import and use the ErrorHandler for better error parsing
        const { ErrorHandler } = await import('../../utils/errorUtils.js');
        ErrorHandler.showError(error, 'Failed to delete lead');
      }
    }
  };

  const getStatusBadge = (status) => {
    // Handle both object and string status values
    let statusValue = 'New';
    if (typeof status === 'object' && status !== null) {
      statusValue = status.name || status.status || 'New';
    } else if (typeof status === 'string') {
      statusValue = status;
    }
    const statusString = String(statusValue).toLowerCase();

    const badgeConfig = {
      'new': { bg: 'bg-success-100', text: 'text-success-800', icon: '🆕' },
      'follow up': { bg: 'bg-warning-100', text: 'text-warning-800', icon: '📞' },
      'call not attended': { bg: 'bg-secondary-100', text: 'text-secondary-800', icon: '📵' },
      'interested': { bg: 'bg-theme-100', text: 'text-theme-800', icon: '👍' },
      'not interested': { bg: 'bg-danger-100', text: 'text-danger-800', icon: '👎' },
      'converted': { bg: 'bg-success-200', text: 'text-success-900', icon: '✅' },
      'lost': { bg: 'bg-danger-200', text: 'text-danger-900', icon: '❌' }
    };

    const config = badgeConfig[statusString] || badgeConfig.new;
    const displayText = statusString.replace(/\b\w/g, l => l.toUpperCase());

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const formatAmount = (amount) => {
    if (!amount) return '-';
    return `₹${parseFloat(amount).toLocaleString()}`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const formatPhoneNumber = (countryCode, phoneNumber) => {
    if (!phoneNumber) return '-';
    const code = countryCode || '+91';
    return `${code} ${phoneNumber}`;
  };



  if (loading && leads.length === 0) {
    return <LoadingScreen />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leads</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your sales leads and track conversion opportunities
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link
            to="/leads/add"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white shadow-sm transition-all duration-200"
            style={{ backgroundColor: currentThemeColor }}
          >
            <FaPlus className="mr-2 h-4 w-4" />
            Add Lead
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      {!statsLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardBody className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FaUser className="h-6 w-6 text-theme-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Leads</p>
                  <p className="text-2xl font-bold text-gray-900">{leadStats.totalLeads || 0}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FaChartLine className="h-6 w-6 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Converted</p>
                  <p className="text-2xl font-bold text-gray-900">{leadStats.convertedLeads || 0}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FaClock className="h-6 w-6 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Follow Up</p>
                  <p className="text-2xl font-bold text-gray-900">{leadStats.followUpLeads || 0}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FaRupeeSign className="h-6 w-6 text-theme-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Conversion Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{leadStats.conversionRate || 0}%</p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Standardized Filters */}
      <StandardFilterLayout
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="Search leads..."
        rightActions={
          <div className="flex items-center gap-3">
            <button
              onClick={() => setViewMode(viewMode === 'table' ? 'card' : 'table')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200"
            >
              {viewMode === 'table' ? <FaTh className="h-3 w-3" /> : <FaList className="h-3 w-3" />}
              <span className="ml-2 hidden sm:inline">
                {viewMode === 'table' ? 'Card View' : 'Table View'}
              </span>
            </button>
          </div>
        }
      >
        {/* Status Filter */}
        <FilterDropdown
          value={filterStatus}
          onChange={setFilterStatus}
          options={[
            { value: 'all', label: 'All Statuses' },
            ...availableStatuses.map(status => ({
              value: status.id,
              label: status.name
            }))
          ]}
          placeholder="All Statuses"
          icon={FaUser}
        />

        {/* Date Range Filter */}
        <div className="flex items-center gap-1">
          <FaCalendar className="text-gray-400" size={14} />
          <DateRangeFilter
            onDateRangeChange={handleDateRangeChange}
            label="Date"
          />
        </div>
      </StandardFilterLayout>

      {/* Leads List */}
      {loading ? (
        <LoadingScreen
          title="Loading Leads..."
          subtitle="Fetching your lead pipeline data"
          variant="page"
        />
      ) : leads.length === 0 ? (
        <Card>
          <CardBody className="p-8 text-center">
            <FaUser className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No leads found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || filterStatus !== 'all' || dateRange
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first lead'}
            </p>
            <Link
              to="/leads/add"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white shadow-sm transition-all duration-200"
              style={{ backgroundColor: currentThemeColor }}
            >
              <FaPlus className="mr-2 h-4 w-4" />
              Add Lead
            </Link>
          </CardBody>
        </Card>
      ) : viewMode === 'table' ? (
        <LeadTable
          leads={leads}
          onEdit={(id) => navigate(`/leads/${id}/edit`)}
          onView={(id) => navigate(`/leads/${id}`)}
          onDelete={handleDelete}
          dropdownOpen={dropdownOpen}
          toggleDropdown={toggleDropdown}
          getStatusBadge={getStatusBadge}
          formatAmount={formatAmount}
          formatDate={formatDate}
          formatPhoneNumber={formatPhoneNumber}
          currentThemeColor={currentThemeColor}
        />
      ) : (
        <LeadCards
          leads={leads}
          onEdit={(id) => navigate(`/leads/${id}/edit`)}
          onView={(id) => navigate(`/leads/${id}`)}
          onDelete={handleDelete}
          dropdownOpen={dropdownOpen}
          toggleDropdown={toggleDropdown}
          getStatusBadge={getStatusBadge}
          formatAmount={formatAmount}
          formatDate={formatDate}
          formatPhoneNumber={formatPhoneNumber}
          currentThemeColor={currentThemeColor}
        />
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((currentPage - 1) * leadsPerPage) + 1} to {Math.min(currentPage * leadsPerPage, totalLeads)} of {totalLeads} leads
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="px-3 py-2 text-sm font-medium text-gray-700">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Lead Table Component - Optimized for laptop screens
const LeadTable = ({
  leads,
  onEdit,
  onView,
  onDelete,
  dropdownOpen,
  toggleDropdown,
  getStatusBadge,
  formatAmount,
  formatDate,
  formatPhoneNumber,
  currentThemeColor
}) => {
  // Define columns with laptop optimization settings
  const columns = [
    {
      header: 'Lead Details',
      key: 'details',
      width: '25%',
      priority: 'high',
      render: (lead) => (
        <div className="min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate" title={lead.customer_name || 'Unnamed Lead'}>
            {lead.customer_name || 'Unnamed Lead'}
          </div>
          <div className="text-xs text-gray-500 truncate" title={typeof lead.products === 'object' ? 'Complex Products' : (lead.products || 'No products specified')}>
            {typeof lead.products === 'object' ? 'Complex Products' : (lead.products || 'No products specified')}
          </div>
          <div className="text-xs text-gray-400">
            {formatDate(lead.created_at)}
          </div>
        </div>
      )
    },
    {
      header: 'Contact',
      key: 'contact',
      width: '15%',
      priority: 'high',
      render: (lead) => (
        <div className="text-sm text-gray-900 truncate" title={formatPhoneNumber(lead.country_code, lead.contact_no)}>
          {formatPhoneNumber(lead.country_code, lead.contact_no)}
        </div>
      )
    },
    {
      header: 'Amount',
      key: 'amount',
      width: '12%',
      priority: 'medium',
      hideOnSmallLaptop: true,
      render: (lead) => (
        <div className="text-sm font-medium text-gray-900">
          {formatAmount(lead.amount)}
        </div>
      )
    },
    {
      header: 'Status',
      key: 'status',
      width: '15%',
      priority: 'high',
      render: (lead) => getStatusBadge(lead.status)
    },
    {
      header: 'Executive',
      key: 'executive',
      width: '15%',
      priority: 'medium',
      hideOnLaptop: true,
      render: (lead) => (
        <div className="text-sm text-gray-900 truncate" title={lead.executive ? `${lead.executive.first_name || ''} ${lead.executive.last_name || ''}`.trim() : '-'}>
          {lead.executive ? `${lead.executive.first_name || ''} ${lead.executive.last_name || ''}`.trim() : '-'}
        </div>
      )
    },
    {
      header: 'Follow Up',
      key: 'followUp',
      width: '10%',
      priority: 'low',
      hideOnLaptop: true,
      render: (lead) => (
        <div className="text-sm text-gray-900">
          {formatDate(lead.follow_up_date)}
        </div>
      )
    },
    {
      header: 'Created',
      key: 'created',
      width: '10%',
      priority: 'medium',
      hideOnSmallLaptop: true,
      render: (lead) => (
        <div className="text-sm text-gray-900">
          {formatDate(lead.created_at)}
        </div>
      )
    },
    {
      header: 'Actions',
      key: 'actions',
      width: '6%',
      priority: 'high',
      truncate: false,
      render: (lead) => (
        <div className="relative inline-block action-dropdown" data-dropdown-container>
          <button
            onClick={(e) => {
              e.stopPropagation();
              toggleDropdown(lead.id);
            }}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150"
            aria-label="Actions"
            aria-expanded={dropdownOpen[lead.id] || false}
            aria-haspopup="true"
          >
            <FaEllipsisV className="h-4 w-4" />
          </button>
          {dropdownOpen[lead.id] && (
            <>
              {/* Backdrop for mobile */}
              <div
                className="fixed inset-0 z-modal-backdrop bg-transparent md:hidden"
                onClick={() => toggleDropdown(lead.id)}
              />
              {/* Dropdown Menu - Absolute positioning with high z-index */}
              <div
                className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-xl border border-gray-200 transform origin-top-right animate-in fade-in-0 zoom-in-95 duration-100 dropdown-menu"
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="actions-menu"
              >
                <div className="py-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onView(lead.id);
                      toggleDropdown(lead.id);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 focus:outline-none focus:bg-gray-100"
                    role="menuitem"
                  >
                    <FaEye className="mr-3 h-4 w-4 text-gray-400" />
                    View Details
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(lead.id);
                      toggleDropdown(lead.id);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 focus:outline-none focus:bg-gray-100"
                    role="menuitem"
                  >
                    <FaEdit className="mr-3 h-4 w-4 text-gray-400" />
                    Edit Lead
                  </button>
                  <hr className="my-1 border-gray-200" />
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(lead.id);
                      toggleDropdown(lead.id);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-900 transition-colors duration-150 focus:outline-none focus:bg-red-50"
                    role="menuitem"
                  >
                    <FaTrash className="mr-3 h-4 w-4 text-red-500" />
                    Delete Lead
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      )
    }
  ];

  return (
    <div className="leads-table-container">
      <ResponsiveTable
        columns={columns}
        data={leads}
        laptopOptimized={true}
        compactMode={true}
        showTooltips={true}
        emptyMessage="No leads found"
      />
    </div>
  );
};

// Lead Cards Component
const LeadCards = ({
  leads,
  onEdit,
  onView,
  onDelete,
  dropdownOpen,
  toggleDropdown,
  getStatusBadge,
  formatAmount,
  formatDate,
  formatPhoneNumber,
  currentThemeColor
}) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4 3xl:grid-cols-6 gap-4">
      {leads.map((lead) => (
        <Card key={lead.id} className="hover:shadow-lg transition-shadow duration-200 border-2 w-full md:w-[250px]" style={{ height: '250px', borderColor: currentThemeColor }}>
          <CardBody className="p-4" style={{ backgroundColor: `${currentThemeColor}10` }}>
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-gray-900 mb-1 truncate">
                  {lead.customer_name || 'Unnamed Lead'}
                </h3>
                <p className="text-xs text-gray-500 mb-1 line-clamp-2">
                  {typeof lead.products === 'object' ? 'Complex Products' : (lead.products || 'No products specified')}
                </p>
                <div className="text-xs text-gray-400">
                  Created: {formatDate(lead.created_at)}
                </div>
              </div>
              <div className="relative inline-block action-dropdown" data-dropdown-container>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleDropdown(lead.id);
                  }}
                  className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150"
                  aria-label="Actions"
                  aria-expanded={dropdownOpen[lead.id] || false}
                  aria-haspopup="true"
                >
                  <FaEllipsisV className="h-4 w-4" />
                </button>
                {dropdownOpen[lead.id] && (
                  <>
                    {/* Backdrop for mobile */}
                    <div
                      className="fixed inset-0 z-modal-backdrop bg-transparent md:hidden"
                      onClick={() => toggleDropdown(lead.id)}
                    />
                    {/* Dropdown Menu */}
                    <div
                      className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-xl border border-gray-200 transform origin-top-right animate-in fade-in-0 zoom-in-95 duration-100 dropdown-menu"
                      role="menu"
                      aria-orientation="vertical"
                      aria-labelledby="actions-menu"
                    >
                      <div className="py-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onView(lead.id);
                            toggleDropdown(lead.id);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 focus:outline-none focus:bg-gray-100"
                          role="menuitem"
                        >
                          <FaEye className="mr-3 h-4 w-4 text-gray-400" />
                          View Details
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onEdit(lead.id);
                            toggleDropdown(lead.id);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 focus:outline-none focus:bg-gray-100"
                          role="menuitem"
                        >
                          <FaEdit className="mr-3 h-4 w-4 text-gray-400" />
                          Edit Lead
                        </button>
                        <hr className="my-1 border-gray-200" />
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onDelete(lead.id);
                            toggleDropdown(lead.id);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-900 transition-colors duration-150 focus:outline-none focus:bg-red-50"
                          role="menuitem"
                        >
                          <FaTrash className="mr-3 h-4 w-4 text-red-500" />
                          Delete Lead
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="space-y-1 text-xs">
              {/* Contact Information */}
              <div className="flex items-center text-gray-600">
                <FaPhone className="mr-1 h-3 w-3" />
                <span className="truncate">{formatPhoneNumber(lead.country_code, lead.contact_no)}</span>
              </div>

              {/* Amount */}
              {lead.amount && (
                <div className="flex items-center text-gray-600">
                  <FaRupeeSign className="mr-1 h-3 w-3" />
                  <span className="font-medium">{formatAmount(lead.amount)}</span>
                </div>
              )}

              {/* Executive */}
              {lead.executive && (
                <div className="flex items-center text-gray-600">
                  <FaUserTie className="mr-1 h-3 w-3" />
                  <span className="truncate">{`${lead.executive.first_name || ''} ${lead.executive.last_name || ''}`.trim()}</span>
                </div>
              )}

              {/* Follow Up Date */}
              {lead.follow_up_date && (
                <div className="flex items-center text-gray-600">
                  <FaCalendar className="mr-1 h-3 w-3" />
                  <span className="truncate">Follow up: {formatDate(lead.follow_up_date)}</span>
                </div>
              )}
            </div>

            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="flex items-center justify-between">
                {getStatusBadge(lead.status)}
                <div className="flex space-x-1">
                  <button
                    onClick={() => onView(lead.id)}
                    className="text-xs font-medium hover:opacity-80"
                    style={{ color: currentThemeColor }}
                  >
                    View
                  </button>
                  <button
                    onClick={() => onEdit(lead.id)}
                    className="text-xs font-medium hover:opacity-80"
                    style={{ color: currentThemeColor }}
                  >
                    Edit
                  </button>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      ))}
    </div>
  );
};

export default LeadList;
