import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardBody, CardHeader } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Spinner } from '../../components/ui/Spinner';
import { executiveAPI } from '../../services/api';
import { formatDate } from '../../utils/dateUtils';
import { FaArrowLeft, FaUser, FaEnvelope, FaPhone, FaMapMarkerAlt, FaCalendarAlt, FaChartBar } from 'react-icons/fa';

const ExecutiveDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [executive, setExecutive] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchExecutiveDetails();
  }, [id]);

  const fetchExecutiveDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 Fetching executive details for ID:', id);
      const response = await executiveAPI.getById(id);
      console.log('📊 Executive API Response:', response);

      if (response.data?.success && response.data?.data?.executive) {
        console.log('✅ Executive data loaded successfully');
        setExecutive(response.data.data.executive);
      } else {
        console.log('❌ Executive not found in response');
        setError('Executive not found');
      }
    } catch (error) {
      console.error('❌ Error fetching executive details:', error);

      // More detailed error handling
      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const message = error.response.data?.message || error.message;

        if (status === 404) {
          setError('Executive not found');
        } else if (status === 401) {
          setError('Authentication required. Please log in again.');
        } else if (status === 403) {
          setError('Access denied. You do not have permission to view this executive.');
        } else {
          setError(`Server error (${status}): ${message}`);
        }
      } else if (error.request) {
        // Network error
        setError('Network error. Please check your connection and try again.');
      } else {
        // Other error
        setError('Failed to load executive details');
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => navigate('/dashboard')} variant="primary">
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  if (!executive) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Executive Not Found</h2>
          <Button onClick={() => navigate('/dashboard')} variant="primary">
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const fullName = `${executive.first_name || ''} ${executive.last_name || ''}`.trim() || 'Unknown Executive';

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate('/dashboard')}
              variant="ghost"
              size="sm"
              className="text-gray-600 hover:text-gray-800"
            >
              <FaArrowLeft className="mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{fullName}</h1>
              <p className="text-gray-600">Executive Details</p>
            </div>
          </div>
          <Badge variant={executive.is_active ? 'success' : 'secondary'}>
            {executive.is_active ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Executive Information */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FaUser className="mr-2 text-blue-600" />
                Executive Information
              </h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Employee Code</label>
                  <p className="text-gray-900">{executive.employee_code || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                  <p className="text-gray-900">{fullName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <p className="text-gray-900 flex items-center">
                    <FaEnvelope className="mr-2 text-gray-400" />
                    {executive.email || 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <p className="text-gray-900 flex items-center">
                    <FaPhone className="mr-2 text-gray-400" />
                    {executive.phone || 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Designation</label>
                  <p className="text-gray-900">{executive.designation?.name || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                  <p className="text-gray-900">{executive.department || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date of Joining</label>
                  <p className="text-gray-900 flex items-center">
                    <FaCalendarAlt className="mr-2 text-gray-400" />
                    {executive.date_of_joining ? formatDate(executive.date_of_joining) : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                  <p className="text-gray-900 flex items-center">
                    <FaMapMarkerAlt className="mr-2 text-gray-400" />
                    {executive.address || 'N/A'}
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Statistics */}
        <div>
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FaChartBar className="mr-2 text-green-600" />
                Statistics
              </h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Assigned Customers</span>
                  <span className="font-semibold text-gray-900">
                    {executive.statistics?.assignedCustomers || 0}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Service Calls</span>
                  <span className="font-semibold text-gray-900">
                    {executive.statistics?.serviceCalls || 0}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Sales</span>
                  <span className="font-semibold text-gray-900">
                    {executive.statistics?.sales || 0}
                  </span>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Additional Information */}
          {executive.notes && (
            <Card className="mt-6">
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">Notes</h3>
              </CardHeader>
              <CardBody>
                <p className="text-gray-700 whitespace-pre-wrap">{executive.notes}</p>
              </CardBody>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExecutiveDetails;
