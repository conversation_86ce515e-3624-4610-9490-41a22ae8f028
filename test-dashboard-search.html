<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Search Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { color: red; }
        .success { color: green; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Dashboard Search Test</h1>
    
    <div class="test-section">
        <h2>Test Executive Search</h2>
        <button onclick="testExecutiveSearch()">Test Executive API</button>
        <div id="executive-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Test Customer Search</h2>
        <button onclick="testCustomerSearch()">Test Customer API</button>
        <div id="customer-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Test Service Search</h2>
        <button onclick="testServiceSearch()">Test Service API</button>
        <div id="service-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Test Mixed Search</h2>
        <input type="text" id="search-term" placeholder="Enter search term" value="test">
        <button onclick="testMixedSearch()">Test Mixed Search</button>
        <div id="mixed-results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('token'),
                        ...options.headers
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('Request failed:', error);
                throw error;
            }
        }
        
        async function testExecutiveSearch() {
            const resultsDiv = document.getElementById('executive-results');
            resultsDiv.innerHTML = '<p>Testing executive search...</p>';
            
            try {
                const data = await makeRequest(`${API_BASE}/executives?search=test&limit=5`);
                resultsDiv.innerHTML = `
                    <div class="success">✅ Executive API Success</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Executive API Error: ${error.message}</div>
                `;
            }
        }
        
        async function testCustomerSearch() {
            const resultsDiv = document.getElementById('customer-results');
            resultsDiv.innerHTML = '<p>Testing customer search...</p>';
            
            try {
                const data = await makeRequest(`${API_BASE}/customers?search=test&limit=5`);
                resultsDiv.innerHTML = `
                    <div class="success">✅ Customer API Success</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Customer API Error: ${error.message}</div>
                `;
            }
        }
        
        async function testServiceSearch() {
            const resultsDiv = document.getElementById('service-results');
            resultsDiv.innerHTML = '<p>Testing service search...</p>';
            
            try {
                const data = await makeRequest(`${API_BASE}/service-calls?search=test&limit=5`);
                resultsDiv.innerHTML = `
                    <div class="success">✅ Service API Success</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Service API Error: ${error.message}</div>
                `;
            }
        }
        
        async function testMixedSearch() {
            const searchTerm = document.getElementById('search-term').value;
            const resultsDiv = document.getElementById('mixed-results');
            resultsDiv.innerHTML = '<p>Testing mixed search...</p>';
            
            try {
                const [customersResponse, serviceCallsResponse, executivesResponse] = await Promise.all([
                    makeRequest(`${API_BASE}/customers?search=${searchTerm}&limit=5`),
                    makeRequest(`${API_BASE}/service-calls?search=${searchTerm}&limit=5`),
                    makeRequest(`${API_BASE}/executives?search=${searchTerm}&limit=5`)
                ]);
                
                const results = [];
                
                // Process customers
                if (customersResponse?.data?.customers) {
                    customersResponse.data.customers.forEach(customer => {
                        results.push({
                            type: 'customer',
                            title: customer.company_name || customer.display_name,
                            id: customer.id
                        });
                    });
                }
                
                // Process services
                if (serviceCallsResponse?.data?.serviceCalls) {
                    serviceCallsResponse.data.serviceCalls.forEach(service => {
                        results.push({
                            type: 'service',
                            title: service.call_number || `SRV-${service.id}`,
                            id: service.id
                        });
                    });
                }
                
                // Process executives
                if (executivesResponse?.data?.executives) {
                    executivesResponse.data.executives.forEach(executive => {
                        results.push({
                            type: 'executive',
                            title: `${executive.first_name || ''} ${executive.last_name || ''}`.trim(),
                            id: executive.id
                        });
                    });
                }
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ Mixed Search Success - Found ${results.length} results</div>
                    <div><strong>Results:</strong></div>
                    ${results.map(r => `<div class="result">${r.type}: ${r.title} (ID: ${r.id})</div>`).join('')}
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Mixed Search Error: ${error.message}</div>
                `;
            }
        }
        
        // Auto-test on page load
        window.onload = function() {
            console.log('Dashboard Search Test Page Loaded');
            console.log('Token:', localStorage.getItem('token') ? 'Present' : 'Missing');
        };
    </script>
</body>
</html>
